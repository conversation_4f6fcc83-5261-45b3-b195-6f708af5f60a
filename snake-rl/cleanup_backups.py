#!/usr/bin/env python3
"""
Cleanup script for old backup files in the Snake RL project.
This script removes old backup files, keeping only the most recent ones.
"""

import os
import sys
from pathlib import Path
from config import Config

def cleanup_old_backups():
    """Clean up old backup files, keeping only the most recent ones"""
    model_dir = Path(Config.MODEL_DIR)

    if not model_dir.exists():
        print(f"Model directory {model_dir} does not exist.")
        return

    # Find all backup files
    backup_files = []
    for f in model_dir.iterdir():
        if f.is_file() and f.suffix == ".npy" and "_backup_" in f.name:
            # Handle files like snake_agent_v2_backup_280500.npy
            backup_files.append(f.name)

    if not backup_files:
        print("No backup files found.")
        return

    print(f"Found {len(backup_files)} backup files:")
    for f in backup_files:
        print(f"  - {f}")

    # Sort by episode number (extracted from filename)
    def extract_episode_number(filename):
        try:
            # Try different patterns
            if "_backup_" in filename:
                # Extract number after _backup_
                parts = filename.split("_backup_")
                if len(parts) == 2:
                    episode_str = parts[1].replace(".npy", "")
                    return int(episode_str)
            return 0
        except (ValueError, IndexError):
            return 0

    backup_files.sort(key=extract_episode_number)

    # Remove old backups, keep only MAX_BACKUPS
    max_backups = getattr(Config, 'MAX_BACKUPS', 5)
    removed_count = 0

    while len(backup_files) > max_backups:
        old_backup = backup_files.pop(0)
        backup_path = model_dir / old_backup

        if backup_path.exists():
            try:
                backup_path.unlink()
                print(f"Removed: {old_backup}")
                removed_count += 1
            except OSError as e:
                print(f"Error removing {old_backup}: {e}")
        else:
            print(f"File not found: {old_backup}")

    print(f"\nCleanup complete:")
    print(f"  - Removed: {removed_count} files")
    print(f"  - Kept: {len(backup_files)} files")
    print(f"  - Max backups: {max_backups}")

def main():
    """Main function"""
    print("Snake RL Backup Cleanup Tool")
    print("=" * 40)

    # Check if we're in the right directory
    if not Path("config.py").exists():
        print("Error: config.py not found. Please run this script from the snake-rl directory.")
        sys.exit(1)

    # Show current backup files
    model_dir = Path(Config.MODEL_DIR)
    if model_dir.exists():
        all_files = list(model_dir.glob("*.npy"))
        backup_files = [f for f in all_files if "backup" in f.name]

        print(f"Current model directory: {model_dir}")
        print(f"Total .npy files: {len(all_files)}")
        print(f"Backup files: {len(backup_files)}")
        print(f"Max backups allowed: {getattr(Config, 'MAX_BACKUPS', 5)}")
        print()

        if backup_files:
            response = input("Do you want to clean up old backup files? (y/N): ")
            if response.lower() in ['y', 'yes']:
                cleanup_old_backups()
            else:
                print("Cleanup cancelled.")
        else:
            print("No backup files found to clean up.")
    else:
        print(f"Model directory {model_dir} does not exist.")

if __name__ == "__main__":
    main()
