import time
from datetime import datetime
from flask import Flask, jsonify, request, render_template_string, send_from_directory
from flask_socketio import <PERSON><PERSON><PERSON>
from threading import Thread, Event
import numpy as np
import os
import logging
from pathlib import Path

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.StreamHandler(),
        logging.FileHandler('snake_rl.log')
    ]
)

# Import game components
import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
os.chdir(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from snake_game import SnakeGame
from agent import RLAgent
from evaluation import Evaluator
from config import Config

app = Flask(__name__, static_folder='static', static_url_path='')
socketio = SocketIO(app, cors_allowed_origins="*")

# Training thread control
training_thread = None
stop_training = Event()

class WebTrainer:
    def __init__(self):
        from config import Config
        self.config = Config
        self.env = SnakeGame()
        self.agent = RLAgent(state_size=(10, 10), action_size=3)

        # Enhanced model loading with priority order
        model_loaded = False
        self.model_loaded_from = "fresh_start"  # Default value

        # Try enhanced model first
        if os.path.exists(Config.MODEL_ENHANCED_PATH):
            model_loaded = self.agent.load(Config.MODEL_ENHANCED_PATH)
            if model_loaded:
                self.model_path = Config.MODEL_ENHANCED_PATH
                self.model_loaded_from = Config.MODEL_ENHANCED_PATH
                logging.info(f"Loaded enhanced model v{Config.MODEL_VERSION}")
        # Try final model
        elif os.path.exists(Config.MODEL_FINAL_PATH):
            model_loaded = self.agent.load(Config.MODEL_FINAL_PATH)
            if model_loaded:
                self.model_path = Config.MODEL_FINAL_PATH
                self.model_loaded_from = Config.MODEL_FINAL_PATH
                logging.info("Loaded final model")
        # Try legacy model
        elif os.path.exists(Config.MODEL_LEGACY_PATH):
            model_loaded = self.agent.load(Config.MODEL_LEGACY_PATH)
            if model_loaded:
                self.model_path = Config.MODEL_LEGACY_PATH
                self.model_loaded_from = Config.MODEL_LEGACY_PATH
                logging.info("Loaded legacy model")

        if not model_loaded:
            # Use enhanced model path for new training
            self.model_path = Config.MODEL_ENHANCED_PATH
            self.model_loaded_from = "fresh_start"
            logging.info("No existing model found, will create new enhanced model")

        # Training stats
        self.episode = 0
        self.scores = []
        self.avg_scores = []

    def reset_training(self):
        """Reset training to start fresh"""
        self.episode = 0
        self.scores = []
        self.avg_scores = []
        self.model_loaded_from = "fresh_start"
        # Create new agent (fresh Q-table)
        self.agent = RLAgent(state_size=(10, 10), action_size=3)
        # Reset episodes_trained to 0 for fresh start
        self.agent.episodes_trained = 0
        # Use enhanced model path for new training
        self.model_path = self.config.MODEL_ENHANCED_PATH
        logging.info("Training reset - starting with fresh model")

    def train(self):
        """Training loop to run in background thread"""
        import time  # Explicit import
        from config import Config

        # Store recent game states for replay
        self.replay_states = {}
        logging.info("Starting training loop")

        while not stop_training.is_set() and self.episode < Config.EPISODES:
            logging.info(f"Starting episode {self.episode + 1}")
            state = self.env.reset()
            episode_states = [state.copy()]
            done = False
            total_reward = 0

            while not done:
                action = self.agent.act(state)
                next_state, reward, done, info = self.env.step(action)
                self.agent.remember(state, action, reward, next_state, done)
                state = next_state
                episode_states.append(state.copy())
                total_reward += reward

                if len(self.agent.memory) > Config.BATCH_SIZE:
                    self.agent.replay(Config.BATCH_SIZE)

            # Use actual game score (fruits eaten), not reward sum
            game_score = info['score']
            self.scores.append(game_score)
            avg_score = np.mean(self.scores[-100:])
            self.avg_scores.append(avg_score)
            self.episode += 1

            # Update agent's episodes_trained counter
            self.agent.episodes_trained += 1

            # Track high score
            if 'high_score' not in self.agent.training_metrics:
                self.agent.training_metrics['high_score'] = 0
            if game_score > self.agent.training_metrics['high_score']:
                self.agent.training_metrics['high_score'] = game_score

            # Store states for replay (keep last 10 episodes)
            self.replay_states[self.episode] = episode_states
            logging.info(f"Stored replay states for episode {self.episode}")
            if len(self.replay_states) > 10:
                oldest = min(self.replay_states.keys())
                del self.replay_states[oldest]
                logging.info(f"Removed replay states for episode {oldest}")

            # Emit training progress (only every 10 episodes to avoid spam)
            if self.episode % 10 == 0:
                socketio.emit('training_update', {
                    'episode': self.episode,
                    'score': game_score,
                    'avg_score': avg_score,
                    'epsilon': self.agent.epsilon,
                    'has_replay': False
                })

            # Save model periodically
            if self.episode % Config.SAVE_INTERVAL == 0:
                self.agent.save(self.model_path)

        try:
            # Save final model and run evaluation
            self.agent.save(self.model_path)
            evaluator = Evaluator(self.model_path)
            final_eval = evaluator.evaluate(num_episodes=10, render=False)

            # Evaluation complete - no replay storage needed

            # Send final evaluation results
            socketio.emit('training_complete', {
                'episode': self.episode,
                'score': self.scores[-1] if self.scores else 0,
                'avg_score': self.avg_scores[-1] if self.avg_scores else 0,
                'epsilon': self.agent.epsilon,
                'evaluation': {
                    'mean_score': float(final_eval['mean_score']),
                    'max_score': float(final_eval['max_score']),
                    'steps': float(final_eval['mean_steps']),
                    'action_distribution': {
                        'left': float(final_eval['action_distribution']['left']),
                        'right': float(final_eval['action_distribution']['right']),
                        'straight': float(final_eval['action_distribution']['straight'])
                    }
                },
                'has_replay': True
            }, callback=lambda: socketio.emit('trigger_replay'))
            logging.info('Training complete event emitted')

        except Exception as e:
            logging.error(f'Training completion error: {str(e)}')
            try:
                current_time = datetime.now().isoformat()
            except Exception:
                current_time = "unknown"

            socketio.emit('training_error', {
                'error': str(e),
                'timestamp': current_time,
                'episode': self.episode,
                'completed': False,
                'time': current_time
            })

            # Ensure training is properly stopped
            stop_training.set()
            # Ensure training is properly stopped
            stop_training.set()

# Initialize trainer and evaluation handler
trainer = None

def get_trainer():
    """Get or create trainer instance"""
    global trainer
    if trainer is None:
        trainer = WebTrainer()
    return trainer

@app.route('/')
def index():
    """Serve the main page"""
    return send_from_directory('static', 'index.html')

@socketio.on('connect')
def handle_connect():
    logging.info(f'Client connected: {request.sid}')

@socketio.on('disconnect')
def handle_disconnect():
    logging.info(f'Client disconnected: {request.sid}')

# Removed replay functionality

@app.route('/api/final_evaluation')
def get_final_evaluation():
    """Fallback endpoint for final evaluation"""
    try:
        trainer = get_trainer()
        evaluator = Evaluator(trainer.model_path)
        final_eval = evaluator.evaluate(num_episodes=10, render=False)
        return jsonify({
            'evaluation': {
                'mean_score': final_eval['mean_score'],
                'max_score': final_eval['max_score'],
                'steps': final_eval['mean_steps'],
                'action_distribution': final_eval['action_distribution']
            }
        })
    except Exception as e:
        return jsonify({'error': str(e)}), 500

@app.route('/api/final_replay')
def get_final_replay():
    """Fallback endpoint for final replay"""
    try:
        trainer = get_trainer()
        evaluator = Evaluator(trainer.model_path)
        final_eval = evaluator.evaluate(num_episodes=1, render=False)

        if hasattr(evaluator, 'last_episode_states'):
            return jsonify({
                'states': evaluator.last_episode_states,
                'evaluation': {
                    'mean_score': final_eval['mean_score'],
                    'max_score': final_eval['max_score'],
                    'steps': final_eval['mean_steps'],
                    'action_distribution': final_eval['action_distribution']
                }
            })
        return jsonify({'error': 'No replay data'}), 404
    except Exception as e:
        return jsonify({'error': str(e)}), 500

@app.route('/api/replay/<int:episode>')
def get_replay(episode):
    """Get game states for a specific episode"""
    trainer = get_trainer()
    logging.info(f"Requested replay for episode {episode}")
    if episode in trainer.replay_states:
        logging.info(f"Found replay data for episode {episode}")
        # Convert numpy arrays to lists for JSON serialization
        states = [state.tolist() for state in trainer.replay_states[episode]]
        return jsonify({
            'states': states,
            'score': trainer.scores[episode-1],
            'steps': len(trainer.replay_states[episode])
        })
    else:
        logging.warning(f"No replay data available for episode {episode}")
        return jsonify({'error': 'Replay not available'}), 404

@app.route('/api/training/start', methods=['POST'])
def start_training():
    """Start training in background thread"""
    global training_thread
    trainer = get_trainer()
    if training_thread is None or not training_thread.is_alive():
        stop_training.clear()
        training_thread = Thread(target=trainer.train)
        training_thread.start()
        return jsonify({'status': 'training started'})
    return jsonify({'status': 'training already running'})

@app.route('/api/training/stop', methods=['POST'])
def stop_training_endpoint():
    """Stop training"""
    stop_training.set()
    return jsonify({'status': 'training stopped'})

@app.route('/api/training/status')
def training_status():
    """Get current training status"""
    trainer = get_trainer()
    is_running = training_thread is not None and training_thread.is_alive()

    # Determine model loaded status and episodes trained
    model_loaded_from = getattr(trainer, 'model_loaded_from', None)
    episodes_trained = getattr(trainer.agent, 'episodes_trained', 0)

    # Always check if model was loaded by looking at memory size and epsilon
    # Override model_loaded_from if it's None/null but we have evidence of a loaded model
    from config import Config
    import os

    # Use the actual episodes_trained from the agent, but provide fallbacks for display
    if model_loaded_from is None or model_loaded_from == "null":
        if len(trainer.agent.memory) > 0 and trainer.agent.epsilon < 0.1:
            # Model was loaded - determine which one based on existing files
            if os.path.exists(Config.MODEL_PATH):
                model_loaded_from = Config.MODEL_PATH
            elif os.path.exists(Config.MODEL_BACKUP_PATH):
                model_loaded_from = Config.MODEL_BACKUP_PATH
            else:
                model_loaded_from = "models/snake_agent_v2.npy"  # Default assumption
        else:
            model_loaded_from = "fresh_start"

    # Final fallback for model_loaded_from
    if model_loaded_from is None:
        if len(trainer.agent.memory) > 0:
            model_loaded_from = "models/snake_agent_v2.npy"
        else:
            model_loaded_from = "fresh_start"

    # Calculate total episodes trained (from loaded model + current session)
    total_episodes_trained = episodes_trained + trainer.episode

    # Calculate high score
    high_score = 0
    if trainer.scores:
        high_score = max(trainer.scores)

    # Check if we have a saved high score from previous sessions
    if hasattr(trainer.agent, 'training_metrics') and 'high_score' in trainer.agent.training_metrics:
        high_score = max(high_score, trainer.agent.training_metrics.get('high_score', 0))

    return jsonify({
        'running': is_running,
        'episode': trainer.episode,  # Current episode in this session
        'total_episodes': total_episodes_trained,  # Total episodes ever trained
        'score': trainer.scores[-1] if trainer.scores else 0,
        'avg_score': trainer.avg_scores[-1] if trainer.avg_scores else 0,
        'high_score': high_score,
        'epsilon': float(trainer.agent.epsilon),
        'memory_size': len(trainer.agent.memory),
        'episodes_trained': episodes_trained,  # Episodes from loaded model
        'completed': trainer.episode >= trainer.config.EPISODES and not is_running,
        'model_loaded_from': model_loaded_from
    })

@app.route('/api/training/debug', methods=['GET'])
def debug_training_status():
    """Debug training status"""
    trainer = get_trainer()
    model_loaded_from = getattr(trainer, 'model_loaded_from', None)
    episodes_trained = getattr(trainer.agent, 'episodes_trained', 0)

    return jsonify({
        'model_loaded_from_raw': model_loaded_from,
        'episodes_trained_raw': episodes_trained,
        'memory_size': len(trainer.agent.memory),
        'epsilon': float(trainer.agent.epsilon),
        'model_loaded_from_type': type(model_loaded_from).__name__,
        'model_loaded_from_is_none': model_loaded_from is None,
        'model_loaded_from_equals_null': model_loaded_from == "null"
    })

@app.route('/api/training/reset', methods=['POST'])
def reset_training_endpoint():
    """Reset training to start fresh"""
    global training_thread
    trainer = get_trainer()

    # Stop current training if running
    if training_thread is not None and training_thread.is_alive():
        stop_training.set()
        training_thread.join(timeout=2)  # Wait for thread to finish

    # Reset trainer
    trainer.reset_training()
    training_thread = None

    return jsonify({'status': 'training reset', 'episode': trainer.episode})

if __name__ == '__main__':
    # Create required directories
    Path("models").mkdir(exist_ok=True)
    Path("plots").mkdir(exist_ok=True)

    # Start Flask app
    socketio.run(app, host='0.0.0.0', port=5001)
