<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Snake RL - Training Dashboard</title>
    <link href="https://fonts.googleapis.com/css2?family=Roboto:wght@400;500&display=swap" rel="stylesheet">
    <style>
        body {
            font-family: 'Roboto', sans-serif;
            margin: 0;
            padding: 20px;
            background-color: #f5f5f5;
            color: #333;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 20px;
        }
        .game-container {
            background: white;
            border-radius: 8px;
            padding: 20px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .controls {
            background: white;
            border-radius: 8px;
            padding: 20px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        canvas {
            background: #222;
            border-radius: 4px;
            display: block;
            margin: 0 auto;
        }
        h1 {
            grid-column: 1 / -1;
            text-align: center;
            color: #2c3e50;
        }
        .stats {
            margin-top: 20px;
        }
        .stat-row {
            display: flex;
            justify-content: space-between;
            margin-bottom: 8px;
        }
        .stat-label {
            font-weight: 500;
        }
        .stat-value {
            font-weight: 400;
        }
        button {
            background: #3498db;
            color: white;
            border: none;
            padding: 10px 15px;
            border-radius: 4px;
            cursor: pointer;
            font-size: 16px;
            margin-right: 10px;
            transition: background 0.2s;
        }
        button:hover {
            background: #2980b9;
        }
        button:disabled {
            background: #95a5a6;
            cursor: not-allowed;
        }
        .replay-controls {
            margin-top: 20px;
        }
        .progress-container {
            margin-top: 20px;
        }
        progress {
            width: 100%;
            height: 10px;
            border-radius: 5px;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>Snake RL Training Dashboard</h1>

        <div class="game-container">
            <h2>Model Evaluation</h2>
            <div id="evaluationResults" style="display: none;">
                <h3>Final Evaluation Results:</h3>
                <div class="eval-stats">
                    <div class="stat-row">
                        <span class="stat-label">Mean Score:</span>
                        <span class="stat-value" id="evalMeanScore">-</span>
                    </div>
                    <div class="stat-row">
                        <span class="stat-label">Max Score:</span>
                        <span class="stat-value" id="evalMaxScore">-</span>
                    </div>
                    <div class="stat-row">
                        <span class="stat-label">Mean Steps:</span>
                        <span class="stat-value" id="evalMeanSteps">-</span>
                    </div>
                </div>
            </div>
        </div>

        <div class="controls">
            <h2>Training Controls</h2>
            <button id="startTraining">▶ Play</button>
            <button id="stopTraining" disabled>⏸ Stop</button>
            <button id="resetTraining">🔄 Reset</button>
            <button id="evaluateModel">📊 Evaluate</button>

            <div class="stats">
                <div class="stat-row">
                    <span class="stat-label">Status:</span>
                    <span class="stat-value" id="trainingStatus">Ready</span>
                </div>
                <div class="stat-row">
                    <span class="stat-label">Connection:</span>
                    <span class="stat-value" id="connectionStatus">Connecting...</span>
                </div>
                <div class="stat-row">
                    <span class="stat-label">Model Loaded:</span>
                    <span class="stat-value" id="modelLoaded">-</span>
                </div>
                <div class="stat-row">
                    <span class="stat-label">Episodes Trained:</span>
                    <span class="stat-value" id="episodesTrained">0</span>
                </div>
                <div class="stat-row">
                    <span class="stat-label">Total Episodes:</span>
                    <span class="stat-value" id="totalEpisodes">0</span>
                </div>
                <div class="stat-row">
                    <span class="stat-label">Current Episode:</span>
                    <span class="stat-value" id="episode">0</span>
                </div>
                <div class="stat-row">
                    <span class="stat-label">Memory Size:</span>
                    <span class="stat-value" id="memorySize">0</span>
                </div>
                <div class="stat-row">
                    <span class="stat-label">Score:</span>
                    <span class="stat-value" id="score">0</span>
                </div>
                <div class="stat-row">
                    <span class="stat-label">High Score:</span>
                    <span class="stat-value" id="highScore">0</span>
                </div>
                <div class="stat-row">
                    <span class="stat-label">Avg Score (100):</span>
                    <span class="stat-value" id="avgScore">0</span>
                </div>
                <div class="stat-row">
                    <span class="stat-label">Exploration Rate:</span>
                    <span class="stat-value" id="epsilon">0</span>
                </div>
            </div>

            <div class="progress-container">
                <progress id="trainingProgress" value="0" max="1000"></progress>
                <div class="stat-row">
                    <span class="stat-label">Progress:</span>
                    <span class="stat-value" id="progressText">0%</span>
                </div>
            </div>

            <div class="status-messages">
                <div id="errorMessage" style="color: red; margin-top: 10px; font-size: 12px;"></div>
            </div>
        </div>
    </div>

    <script src="app.js"></script>
</body>
</html>
