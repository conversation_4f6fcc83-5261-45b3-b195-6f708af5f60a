// Simple dashboard without replay functionality

// DOM elements
const startTrainingBtn = document.getElementById('startTraining');
const stopTrainingBtn = document.getElementById('stopTraining');
const resetTrainingBtn = document.getElementById('resetTraining');
const evaluateBtn = document.getElementById('evaluateModel');
const episodeEl = document.getElementById('episode');
const scoreEl = document.getElementById('score');
const avgScoreEl = document.getElementById('avgScore');
const epsilonEl = document.getElementById('epsilon');
const progressBar = document.getElementById('trainingProgress');
const progressText = document.getElementById('progressText');
const modelLoadedEl = document.getElementById('modelLoaded');

// API configuration
const API_BASE = '/api';

// Connection status
document.getElementById('connectionStatus').textContent = 'REST API';
document.getElementById('connectionStatus').style.color = 'blue';

// Auto-refresh interval - poll every 2 seconds
let refreshInterval = setInterval(() => {
    if (document.hidden) return;
    fetchTrainingStatus();
}, 2000);

async function fetchTrainingStatus() {
    try {
        const response = await fetch(`${API_BASE}/training/status`);
        const data = await response.json();

        // Update model loaded info with better fallback logic
        if (data.model_loaded_from !== undefined && data.model_loaded_from !== null && data.model_loaded_from !== "null") {
            let modelInfo;
            if (data.model_loaded_from === "fresh_start") {
                modelInfo = "Fresh Start";
                modelLoadedEl.style.color = 'orange';
            } else if (data.model_loaded_from) {
                // Extract meaningful model name
                const modelName = data.model_loaded_from
                    .replace('models/', '')
                    .replace('.npy', '')
                    .replace('snake_agent_', '')
                    .replace('_', ' ');
                modelInfo = modelName.charAt(0).toUpperCase() + modelName.slice(1);
                modelLoadedEl.style.color = 'green';
            } else {
                modelInfo = "Unknown";
                modelLoadedEl.style.color = 'gray';
            }
            modelLoadedEl.textContent = modelInfo;
        } else {
            // Fallback: if we have memory and low epsilon, assume model is loaded
            if (data.memory_size > 0 && data.epsilon < 0.1) {
                modelLoadedEl.textContent = "V2 (detected)";
                modelLoadedEl.style.color = 'green';
            } else {
                modelLoadedEl.textContent = "Fresh Start";
                modelLoadedEl.style.color = 'orange';
            }
        }

        // Update additional model information
        if (data.episodes_trained !== undefined) {
            document.getElementById('episodesTrained').textContent = data.episodes_trained;
        }
        if (data.memory_size !== undefined) {
            document.getElementById('memorySize').textContent = data.memory_size;
        }

        if (data.running) {
            updateTrainingStats(data);
            document.getElementById('trainingStatus').textContent = 'Training Running';
            document.getElementById('trainingStatus').style.color = 'blue';
            document.getElementById('startTraining').disabled = true;
            document.getElementById('stopTraining').disabled = false;
            document.getElementById('resetTraining').disabled = true;
        } else if (data.episode > 0) {
            // Training completed
            document.getElementById('trainingStatus').textContent = 'Training Complete';
            document.getElementById('trainingStatus').style.color = 'green';
            document.getElementById('startTraining').disabled = false;
            document.getElementById('stopTraining').disabled = true;
            document.getElementById('resetTraining').disabled = false;

            // Update final stats with correct progress calculation
            document.getElementById('episode').textContent = data.episode;
            document.getElementById('score').textContent = data.score || 0;
            document.getElementById('avgScore').textContent = data.avg_score?.toFixed(1) || '0.0';
            document.getElementById('epsilon').textContent = data.epsilon?.toFixed(4) || '0.0000';

            // Update progress correctly
            const totalEpisodes = 1000;
            const progressPercent = (data.episode / totalEpisodes) * 100;
            document.getElementById('trainingProgress').value = data.episode;
            document.getElementById('progressText').textContent = `${progressPercent.toFixed(1)}% (${data.episode}/${totalEpisodes})`;
        } else {
            document.getElementById('trainingStatus').textContent = 'Ready';
            document.getElementById('trainingStatus').style.color = 'gray';
            document.getElementById('resetTraining').disabled = false;
        }
    } catch (error) {
        console.error('Status check error:', error);
        document.getElementById('trainingStatus').textContent = 'Connection Error';
        document.getElementById('trainingStatus').style.color = 'red';
    }
}

function updateTrainingStats(data) {
    if (episodeEl) episodeEl.textContent = data.episode || 0;
    if (scoreEl) scoreEl.textContent = data.score || 0;
    if (avgScoreEl) avgScoreEl.textContent = data.avg_score?.toFixed(1) || '0.0';
    if (epsilonEl) epsilonEl.textContent = data.epsilon?.toFixed(4) || '0.0000';

    // Update additional information
    if (data.episodes_trained !== undefined) {
        document.getElementById('episodesTrained').textContent = data.episodes_trained;
    }
    if (data.memory_size !== undefined) {
        document.getElementById('memorySize').textContent = data.memory_size;
    }

    // Update progress bar correctly
    const episode = data.episode || 0;
    const totalEpisodes = 1000;
    progressBar.max = totalEpisodes;
    progressBar.value = episode;
    const progressPercent = (episode / totalEpisodes) * 100;
    progressText.textContent = `${progressPercent.toFixed(1)}% (${episode}/${totalEpisodes})`;
}

function showFinalEvaluation(evaluation) {
    console.log('Showing final evaluation:', evaluation);

    // Update evaluation display
    document.getElementById('evalMeanScore').textContent = evaluation.mean_score.toFixed(2);
    document.getElementById('evalMaxScore').textContent = evaluation.max_score;
    document.getElementById('evalMeanSteps').textContent = evaluation.steps.toFixed(1);

    // Show evaluation results
    document.getElementById('evaluationResults').style.display = 'block';
}

async function startTraining() {
    try {
        const response = await fetch(`${API_BASE}/training/start`, {
            method: 'POST'
        });
        const data = await response.json();
        console.log(data.status);

        startTrainingBtn.disabled = true;
        stopTrainingBtn.disabled = false;
        document.getElementById('trainingStatus').textContent = 'Training Starting...';
        document.getElementById('trainingStatus').style.color = 'orange';
    } catch (error) {
        console.error('Error starting training:', error);
        document.getElementById('errorMessage').textContent = `Training error: ${error.message}`;
    }
}

async function stopTraining() {
    try {
        const response = await fetch(`${API_BASE}/training/stop`, {
            method: 'POST'
        });
        const data = await response.json();
        console.log(data.status);

        startTrainingBtn.disabled = false;
        stopTrainingBtn.disabled = true;
    } catch (error) {
        console.error('Error stopping training:', error);
    }
}

async function resetTraining() {
    if (!confirm('Are you sure you want to reset training? This will start with a fresh model and lose all progress.')) {
        return;
    }

    try {
        resetTrainingBtn.disabled = true;
        resetTrainingBtn.textContent = 'Resetting...';

        const response = await fetch(`${API_BASE}/training/reset`, {
            method: 'POST'
        });
        const data = await response.json();
        console.log(data.status);

        // Reset UI
        document.getElementById('episode').textContent = '0';
        document.getElementById('episodesTrained').textContent = '0';
        document.getElementById('memorySize').textContent = '0';
        document.getElementById('score').textContent = '0';
        document.getElementById('avgScore').textContent = '0.0';
        document.getElementById('epsilon').textContent = '1.0000';
        document.getElementById('trainingProgress').value = 0;
        document.getElementById('progressText').textContent = '0.0% (0/1000)';
        document.getElementById('trainingStatus').textContent = 'Reset Complete';
        document.getElementById('trainingStatus').style.color = 'green';
        modelLoadedEl.textContent = 'Fresh Start';
        modelLoadedEl.style.color = 'orange';

        resetTrainingBtn.textContent = '🔄 Reset';
        resetTrainingBtn.disabled = false;
        startTrainingBtn.disabled = false;
        stopTrainingBtn.disabled = true;
    } catch (error) {
        console.error('Error resetting training:', error);
        document.getElementById('errorMessage').textContent = `Reset error: ${error.message}`;
        resetTrainingBtn.textContent = '🔄 Reset';
        resetTrainingBtn.disabled = false;
    }
}

async function evaluateModel() {
    console.log('Evaluate button clicked!');
    try {
        evaluateBtn.disabled = true;
        evaluateBtn.textContent = 'Evaluating...';

        console.log('Fetching evaluation from:', `${API_BASE}/final_evaluation`);
        const response = await fetch(`${API_BASE}/final_evaluation`);
        const data = await response.json();

        console.log('Evaluation response:', data);

        if (data.error) {
            throw new Error(data.error);
        }

        // Show evaluation results
        showFinalEvaluation(data.evaluation);

        evaluateBtn.textContent = '📊 Evaluate';
        evaluateBtn.disabled = false;
    } catch (error) {
        console.error('Error evaluating model:', error);
        const errorEl = document.getElementById('errorMessage');
        if (errorEl) {
            errorEl.textContent = `Evaluation error: ${error.message}`;
        }
        evaluateBtn.textContent = '📊 Evaluate';
        evaluateBtn.disabled = false;
    }
}

// Event listeners
startTrainingBtn.addEventListener('click', startTraining);
stopTrainingBtn.addEventListener('click', stopTraining);
resetTrainingBtn.addEventListener('click', resetTraining);
evaluateBtn.addEventListener('click', evaluateModel);

// Initialize
(async function() {
    try {
        console.log('Initial status check...');
        const response = await fetch(`${API_BASE}/training/status`);
        const data = await response.json();

        console.log('Initial status response:', data);

        if (data.running) {
            startTrainingBtn.disabled = true;
            stopTrainingBtn.disabled = false;
            updateTrainingStats(data);
        } else {
            // Update with current data even if not running
            fetchTrainingStatus();
        }
    } catch (error) {
        console.error('Error checking training status:', error);
    }
})();