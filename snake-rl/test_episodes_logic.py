#!/usr/bin/env python3
"""
Test script to verify the episodes_trained logic works correctly.
"""

import os
import sys
from agent import R<PERSON><PERSON>

def test_episodes_logic():
    """Test the episodes_trained logic"""
    print("Testing Episodes Logic")
    print("=" * 40)
    
    # Create a fresh agent
    agent = RLAgent(state_size=(10, 10), action_size=3)
    
    print(f"Fresh agent episodes_trained: {agent.episodes_trained}")
    print(f"Fresh agent high_score: {agent.training_metrics.get('high_score', 0)}")
    
    # Simulate training 100 episodes
    print("\nSimulating 100 episodes of training...")
    for i in range(100):
        # Simulate a score
        score = i % 10  # Scores 0-9
        if score > agent.training_metrics['high_score']:
            agent.training_metrics['high_score'] = score
    
    # Update episodes_trained as if training completed
    agent.episodes_trained += 100
    
    print(f"After 100 episodes:")
    print(f"  episodes_trained: {agent.episodes_trained}")
    print(f"  high_score: {agent.training_metrics['high_score']}")
    
    # Save the model
    test_model_path = "models/test_model.npy"
    agent.save(test_model_path)
    print(f"\nModel saved to {test_model_path}")
    
    # Create a new agent and load the model
    print("\nLoading model into new agent...")
    new_agent = RLAgent(state_size=(10, 10), action_size=3)
    
    print(f"New agent before loading:")
    print(f"  episodes_trained: {new_agent.episodes_trained}")
    print(f"  high_score: {new_agent.training_metrics.get('high_score', 0)}")
    
    # Load the model
    success = new_agent.load(test_model_path)
    
    if success:
        print(f"\nNew agent after loading:")
        print(f"  episodes_trained: {new_agent.episodes_trained}")
        print(f"  high_score: {new_agent.training_metrics.get('high_score', 0)}")
        
        # Simulate another 50 episodes
        print("\nSimulating another 50 episodes...")
        for i in range(50):
            score = (i + 5) % 12  # Scores 5-11, 0-4
            if score > new_agent.training_metrics['high_score']:
                new_agent.training_metrics['high_score'] = score
        
        # Update episodes_trained for the additional training
        new_agent.episodes_trained += 50
        
        print(f"After additional 50 episodes:")
        print(f"  episodes_trained: {new_agent.episodes_trained}")
        print(f"  high_score: {new_agent.training_metrics['high_score']}")
        
        # Save again
        new_agent.save(test_model_path)
        print(f"\nUpdated model saved")
        
        # Test the final logic
        print("\n" + "=" * 40)
        print("EXPECTED BEHAVIOR:")
        print("  episodes_trained: 150 (100 + 50)")
        print("  high_score: 11 (max from both sessions)")
        print("  current_episode: 0 (when not training)")
        print("  total_episodes: 150 + 0 = 150")
        
    else:
        print("Failed to load model!")
    
    # Cleanup
    if os.path.exists(test_model_path):
        os.remove(test_model_path)
        print(f"\nCleaned up {test_model_path}")

if __name__ == "__main__":
    # Make sure we're in the right directory
    if not os.path.exists("config.py"):
        print("Error: Please run this script from the snake-rl directory")
        sys.exit(1)
    
    # Create models directory if it doesn't exist
    os.makedirs("models", exist_ok=True)
    
    test_episodes_logic()
